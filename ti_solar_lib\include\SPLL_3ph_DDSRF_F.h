/*
 * =====================================================================================
 * 文件名: SPLL_3ph_DDSRF_F.h
 * 功能描述: 三相双同步参考坐标系锁相环(Three-Phase Dual Decoupled Synchronous Reference Frame PLL)
 *
 * 主要功能:
 * - 实现三相电网电压的相位和频率检测
 * - 采用双同步参考坐标系(DDSRF)技术，提高不平衡电网条件下的性能
 * - 用于太阳能逆变器与电网的精确同步
 * - 支持电网频率变化和电压不平衡的鲁棒性检测
 *
 * 技术特点:
 * - 基于正负序分离的双DQ坐标变换
 * - 内置解耦网络消除正负序分量间的相互干扰
 * - 多级低通滤波器提高噪声抑制能力
 * - 自适应频率跟踪，适应电网频率波动
 *
 * 适用场景:
 * - 三相并网逆变器的电网同步
 * - 电网电压不平衡条件下的相位检测
 * - 微电网和分布式发电系统
 * =====================================================================================
 */

#ifndef SPLL_3ph_DDSRF_F_H_
#define SPLL_3ph_DDSRF_F_H_

//*********** 结构体定义 ********//

/**
 * @brief 锁相环低通滤波器系数结构体
 * @details 定义环路滤波器的传递函数系数，用于控制锁相环的动态响应特性
 */
typedef struct
{
    float32 B1_lf;      // 环路滤波器分子系数B1 (前一采样点系数)
    float32 B0_lf;      // 环路滤波器分子系数B0 (当前采样点系数)
    float32 A1_lf;      // 环路滤波器分母系数A1 (反馈系数，通常为-1.0)
} SPLL_3ph_DDSRF_F_LPF_COEFF;

/**
 * @brief 三相双同步参考坐标系锁相环主结构体
 * @details 包含锁相环运算所需的所有状态变量、系数和中间计算结果
 *
 * 工作原理:
 * 1. 三相电压经Clarke变换得到αβ坐标系分量
 * 2. 通过正负序分离得到d_p,q_p(正序)和d_n,q_n(负序)分量
 * 3. 解耦网络消除正负序分量间的耦合干扰
 * 4. 低通滤波器滤除高频噪声和谐波
 * 5. 环路滤波器产生频率控制信号
 * 6. VCO(压控振荡器)积分产生相位角theta
 */
typedef struct
{
    // ========== 正负序DQ分量 (输入信号) ==========
    float32 d_p;        // 正序d轴分量 (与电网电压同相分量)
    float32 d_n;        // 负序d轴分量 (电网不平衡时的负序同相分量)
    float32 q_p;        // 正序q轴分量 (与电网电压正交分量，锁相时应为0)
    float32 q_n;        // 负序q轴分量 (电网不平衡时的负序正交分量)

    // ========== 解耦网络输出 ==========
    float32 d_p_decoupl;    // 解耦后的正序d轴分量 (消除负序干扰)
    float32 d_n_decoupl;    // 解耦后的负序d轴分量 (消除正序干扰)
    float32 q_p_decoupl;    // 解耦后的正序q轴分量 (用于相位误差检测)
    float32 q_n_decoupl;    // 解耦后的负序q轴分量 (负序相位误差)

    // ========== 解耦网络三角函数 ==========
    float32 cos_2theta;     // cos(2θ) - 用于正负序解耦的二倍频余弦
    float32 sin_2theta;     // sin(2θ) - 用于正负序解耦的二倍频正弦

    // ========== 低通滤波器状态变量 ==========
    float32 y[2];       // 正序d轴低通滤波器状态 [当前值, 前一值]
    float32 x[2];       // 正序q轴低通滤波器状态 [当前值, 前一值]
    float32 w[2];       // 负序d轴低通滤波器状态 [当前值, 前一值]
    float32 z[2];       // 负序q轴低通滤波器状态 [当前值, 前一值]
    float32 k1;         // 低通滤波器系数1 (输入增益)
    float32 k2;         // 低通滤波器系数2 (反馈增益)

    // ========== 滤波后的解耦分量 ==========
    float32 d_p_decoupl_lpf;    // 低通滤波后的正序d轴分量
    float32 d_n_decoupl_lpf;    // 低通滤波后的负序d轴分量
    float32 q_p_decoupl_lpf;    // 低通滤波后的正序q轴分量
    float32 q_n_decoupl_lpf;    // 低通滤波后的负序q轴分量

    // ========== 环路滤波器和VCO ==========
    float32 v_q[2];     // 环路滤波器输入信号 [当前值, 前一值]
    float32 theta[2];   // 输出相位角 [当前值, 前一值] (弧度)
    float32 ylf[2];     // 环路滤波器输出 [当前值, 前一值] (频率误差)
    float32 fo;         // 输出频率 (Hz) - 检测到的电网频率
    float32 fn;         // 标称频率 (Hz) - 电网额定频率(50Hz或60Hz)
    float32 delta_T;    // 采样周期 (秒) - 通常为PWM周期(如50μs@20kHz)

    // ========== 环路滤波器系数 ==========
    SPLL_3ph_DDSRF_F_LPF_COEFF lpf_coeff;  // 环路滤波器传递函数系数
} SPLL_3ph_DDSRF_F;

//*********** 函数声明 *******//

/**
 * @brief 三相双同步参考坐标系锁相环初始化函数
 * @details 初始化锁相环的所有状态变量和系数，为正常运行做准备
 *
 * @param Grid_freq 电网标称频率 (Hz)
 *                  - 50Hz: 中国、欧洲等地区的工频
 *                  - 60Hz: 美国、日本等地区的工频
 * @param DELTA_T   采样周期 (秒)
 *                  - 通常等于PWM控制周期
 *                  - 例如: 50e-6 (对应20kHz PWM频率)
 * @param k1        低通滤波器输入增益系数
 *                  - 影响滤波器的截止频率和响应速度
 *                  - 典型值: 0.1 ~ 1.0
 * @param k2        低通滤波器反馈增益系数
 *                  - 与k1配合决定滤波器特性
 *                  - 典型值: 0.9 ~ 0.99
 * @param spll      指向锁相环结构体的指针
 *
 * @note 调用此函数后，锁相环处于初始状态，需要几个电网周期才能稳定锁定
 * @warning 必须在使用SPLL_3ph_DDSRF_F_FUNC之前调用此初始化函数
 */
void SPLL_3ph_DDSRF_F_init(int Grid_freq, float32 DELTA_T, float32 k1, float32 k2, SPLL_3ph_DDSRF_F *spll);

/**
 * @brief 三相双同步参考坐标系锁相环主运算函数
 * @details 执行完整的锁相环算法，包括解耦、滤波、环路滤波和VCO运算
 *
 * 调用前需要准备的数据:
 * - spll_obj->d_p, d_n, q_p, q_n: 正负序DQ分量 (通过ABC_DQ0_POS_NEG变换获得)
 * - spll_obj->cos_2theta, sin_2theta: 二倍频三角函数 (基于前一次的theta计算)
 *
 * 函数执行的主要步骤:
 * 1. 解耦网络: 消除正负序分量间的相互干扰
 * 2. 低通滤波: 滤除高频噪声和谐波分量
 * 3. 环路滤波: 产生频率控制信号
 * 4. VCO积分: 生成相位角theta
 *
 * 输出结果:
 * - spll_obj->theta[0]: 当前检测到的电网相位角 (弧度, 0~2π)
 * - spll_obj->fo: 当前检测到的电网频率 (Hz)
 *
 * @param spll_obj 指向锁相环结构体的指针
 *
 * @note 此函数应在每个PWM周期调用一次，通常在ADC中断中执行
 * @note 函数执行时间约为几十微秒，需要考虑实时性要求
 * @warning 输入的DQ分量必须是标准化的单位值 (标幺值)
 */
void SPLL_3ph_DDSRF_F_FUNC(SPLL_3ph_DDSRF_F *spll_obj);

//*********** 宏定义 ***********//

/**
 * @brief 三相双同步参考坐标系锁相环快速运算宏
 * @details 提供与SPLL_3ph_DDSRF_F_FUNC函数相同功能的内联宏实现，用于对执行速度要求极高的场合
 *
 * 宏的优势:
 * - 避免函数调用开销，执行速度更快
 * - 适合在高频中断(如20kHz PWM中断)中使用
 * - 代码直接展开，便于编译器优化
 *
 * 算法流程详解:
 * 1. 解耦网络 (Decoupling Network):
 *    - 消除正负序DQ分量间的二倍频耦合干扰
 *    - 使用cos(2θ)和sin(2θ)进行坐标变换
 *    - 实现正负序分量的完全解耦
 *
 * 2. 低通滤波器 (Low Pass Filter):
 *    - 对解耦后的四个分量分别进行低通滤波
 *    - 滤除高频噪声和开关谐波
 *    - 采用一阶IIR滤波器结构: y[n] = k1*x[n] - k2*y[n-1]
 *
 * 3. 环路滤波器 (Loop Filter):
 *    - 对正序q轴分量进行PI控制
 *    - 产生频率误差信号用于VCO控制
 *    - 决定锁相环的动态响应特性
 *
 * 4. 压控振荡器 (VCO):
 *    - 将频率信号积分得到相位角
 *    - 包含频率自适应滤波，提高电压跳变时的稳定性
 *    - 相位角限制在0~2π范围内
 *
 * @param spll_obj 锁相环结构体对象 (注意：传入的是对象本身，不是指针)
 *
 * 使用示例:
 * @code
 * SPLL_3ph_DDSRF_F my_spll;
 * // ... 初始化和设置输入数据 ...
 * SPLL_3ph_DDSRF_F_MACRO(my_spll);  // 执行锁相环运算
 * float phase = my_spll.theta[0];   // 获取检测到的相位角
 * float freq = my_spll.fo;          // 获取检测到的频率
 * @endcode
 *
 * @note 使用前必须正确设置d_p, d_n, q_p, q_n和cos_2theta, sin_2theta
 * @note 此宏会修改结构体中的多个成员变量
 * @warning 宏展开后代码较长，会增加程序存储器占用
 */
#define SPLL_3ph_DDSRF_F_MACRO(spll_obj)                                                                                                      \
    /* ========== 解耦网络运算 ========== */                                                                                                    \
    /* 正序分量解耦: 消除负序分量的干扰 */                                                                                                        \
    spll_obj.d_p_decoupl = spll_obj.d_p - (spll_obj.d_n_decoupl_lpf * spll_obj.cos_2theta) - (spll_obj.q_n_decoupl * spll_obj.sin_2theta);    \
    spll_obj.q_p_decoupl = spll_obj.q_p + (spll_obj.d_n_decoupl_lpf * spll_obj.sin_2theta) - (spll_obj.q_n_decoupl * spll_obj.cos_2theta);    \
    /* 负序分量解耦: 消除正序分量的干扰 */                                                                                                        \
    spll_obj.d_n_decoupl = spll_obj.d_n - (spll_obj.d_p_decoupl_lpf * spll_obj.cos_2theta) + (spll_obj.q_p_decoupl * spll_obj.sin_2theta);    \
    spll_obj.q_n_decoupl = spll_obj.q_n - (spll_obj.d_p_decoupl_lpf * spll_obj.sin_2theta) - (spll_obj.q_p_decoupl * spll_obj.cos_2theta);    \
    \
    /* ========== 低通滤波器运算 ========== */                                                                                                  \
    /* 正序d轴分量滤波 */                                                                                                                      \
    spll_obj.y[1] = (spll_obj.d_p_decoupl * spll_obj.k1) - (spll_obj.y[0] * spll_obj.k2);                                                     \
    spll_obj.d_p_decoupl_lpf = spll_obj.y[1] + spll_obj.y[0];                                                                                 \
    spll_obj.y[0] = spll_obj.y[1];                                                                                                            \
    /* 正序q轴分量滤波 */                                                                                                                      \
    spll_obj.x[1] = (spll_obj.q_p_decoupl * spll_obj.k1) - (spll_obj.x[0] * spll_obj.k2);                                                     \
    spll_obj.q_p_decoupl_lpf = spll_obj.x[1] + spll_obj.x[0];                                                                                 \
    spll_obj.x[0] = spll_obj.x[1];                                                                                                            \
    /* 负序d轴分量滤波 */                                                                                                                      \
    spll_obj.w[1] = (spll_obj.d_n_decoupl * spll_obj.k1) - (spll_obj.w[0] * spll_obj.k2);                                                     \
    spll_obj.d_n_decoupl_lpf = spll_obj.w[1] + spll_obj.w[0];                                                                                 \
    spll_obj.w[0] = spll_obj.w[1];                                                                                                            \
    /* 负序q轴分量滤波 */                                                                                                                      \
    spll_obj.z[1] = (spll_obj.q_n_decoupl * spll_obj.k1) - (spll_obj.z[0] * spll_obj.k2);                                                     \
    spll_obj.q_n_decoupl_lpf = spll_obj.z[1] + spll_obj.z[0];                                                                                 \
    spll_obj.z[0] = spll_obj.z[1];                                                                                                            \
    \
    /* ========== 环路滤波器运算 ========== */                                                                                                  \
    spll_obj.v_q[0] = spll_obj.q_p_decoupl;  /* 使用正序q轴分量作为相位误差信号 */                                                               \
    /* PI控制器: 将相位误差转换为频率控制信号 */                                                                                                  \
    spll_obj.ylf[0] = spll_obj.ylf[1] + (spll_obj.lpf_coeff.B0_lf * spll_obj.v_q[0]) + (spll_obj.lpf_coeff.B1_lf * spll_obj.v_q[1]);          \
    spll_obj.ylf[1] = spll_obj.ylf[0];                                                                                                        \
    spll_obj.v_q[1] = spll_obj.v_q[0];                                                                                                        \
    \
    /* ========== VCO (压控振荡器) 运算 ========== */                                                                                           \
    /* spll_obj.fo = spll_obj.fn + spll_obj.ylf[0]; */                      /* 原始频率计算公式 */                                              \
    spll_obj.fo += ((spll_obj.fn + spll_obj.ylf[0]) - spll_obj.fo) * 0.0001f; /* 频率自适应滤波: 电压跳变时防止频率抖动导致相位偏差 */            \
    \
    /* 相位积分: θ[n] = θ[n-1] + 2π*f*ΔT */                                                                                                    \
    spll_obj.theta[0] = spll_obj.theta[1] + ((spll_obj.fo * spll_obj.delta_T) * (float32)(2.0 * 3.1415926));                                  \
    /* 相位角限制在0~2π范围内 */                                                                                                                \
    if (spll_obj.theta[0] > (float32)(2.0 * 3.1415926))                                                                                       \
        spll_obj.theta[0] = spll_obj.theta[0] - (float32)(2.0 * 3.1415926);                                                                   \
    spll_obj.theta[1] = spll_obj.theta[0];  /* 保存当前值作为下次计算的前一值 */

#endif /* SPLL_3ph_DDSRF_F_H_ */
